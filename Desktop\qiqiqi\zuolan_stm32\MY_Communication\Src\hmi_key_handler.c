/**
 * @file hmi_key_handler.c
 * @brief 串口屏按键处理模块
 * @details 该模块负责处理来自串口屏的按键数据，实现按键功能映射
 * <AUTHOR>
 * @date 2025-07-24
 */

#include "hmi_key_handler.h"
#include "my_usart.h"
#include "my_hmi.h"
#include "key_app.h"
#include "bsp_system.h"
#include "stdlib.h"  // 用于atoi函数
#include "stdio.h"   // 用于sprintf函数

// DDS控制相关变量
static uint8_t dds_output_enabled = 0;  // DDS输出使能状态：0=关闭，1=开启
static uint32_t dds_frequency = 1000; // DDS输出频率，默认1MHz
static uint8_t dds_channel = 1;          // DDS输出通道，默认通道1
static uint16_t dds_amplitude = 1023;    // DDS输出幅度，默认最大值
static float dds_phase = 0.0f;           // DDS输出相位，默认0度
static uint8_t dds_waveform_type = 0;    // DDS波形类型：0=正弦波，1=方波，2=三角波

// 串口屏参数设置相关变量
float freq_my = 1.0f;           // 频率参数，单位MHz
int vpp_my = 1000;              // 电压峰峰值参数，单位mV
static int temp_index = 0;      // 临时索引变量
static int temp_int = 0;        // 临时整数变量
static char change_flag = 0;    // 参数变更标志
static char temp_str_to_int[20]; // 字符串转整数临时缓冲区

// 波形类型字符串数组
static const char* waveform_names[] = {
    "SINE",     // 正弦波
    "SQUARE",   // 方波
    "TRIANGLE"  // 三角波
};

// DDS控制函数声明
static void DDS_Enable_Output(void);     // 开启DDS输出
static void DDS_Disable_Output(void);    // 关闭DDS输出
static void DDS_Increase_Frequency(void); // 增加频率100Hz
static void DDS_Decrease_Frequency(void); // 减少频率100Hz
static void DDS_Update_Display(void);    // 更新串口屏显示
static void Process_Parameter_Command(uint16_t len); // 处理参数设置命令

// 临时变量
static unsigned char len1; // 接收到的数据长度
static char str[100];      // 字符串缓冲区
static int i;              // 循环变量


/**
 * @brief 串口屏按键处理主函数
 * @details 检测串口屏按键数据并执行相应功能
 * @param None
 * @retval None
 */
void HMI_Key_Process(void)
{
    if (USART_RX_STA & 0x8000) // 检查是否接收到数据
    {
        uint16_t len = USART_RX_STA & 0x3fff; // 获取数据长度

        switch (USART_RX_BUF[0])
        {
            case '1': // 按键1：开启DDS输出
            {
                DDS_Enable_Output();
                my_printf(&huart1, "HMI按键1：DDS输出已开启，频率=%luHz\r\n", dds_frequency);
                break;
            }

            case '2': // 按键2：暂停DDS输出
            {
                DDS_Disable_Output();
                my_printf(&huart1, "HMI按键2：DDS输出已暂停\r\n");
                break;
            }

            case '3': // 按键3：步进100Hz
            {
                DDS_Increase_Frequency();
                my_printf(&huart1, "HMI按键3：频率增加100Hz，当前频率=%luHz\r\n", dds_frequency);
                break;
            }

            case '4': // 按键4：减100Hz
            {
                DDS_Decrease_Frequency();
                my_printf(&huart1, "HMI按键4：频率减少100Hz，当前频率=%luHz\r\n", dds_frequency);
                break;
            }

            case 'A': // 参数设置命令：A + 参数类型 + 数值 + ,
            {
                Process_Parameter_Command(len);
                break;
            }

            case '1': // 简单命令：发送计数器到串口屏
            {
                static int counter = 0;
                counter++;
                sprintf(str, "%d", counter);
                HMI_send_string("t0.txt", str); // 发送到串口屏文本框t0
                my_printf(&huart1, "HMI命令1：计数器=%d\r\n", counter);
                break;
            }

            case '5': // 按键5：与硬件按键2功能一致（保持原功能）
            {

                break;
            }

            default:
            {
                my_printf(&huart1, "未知HMI按键：%c\r\n", USART_RX_BUF[0]);
                break;
            }
        }

        USART_RX_STA = 0; // 清除接收状态标志
    }
}


/**
 * @brief 获取当前按键状态
 * @details 返回当前按下的按键值
 * @param None
 * @retval uint8_t 按键值('1'-'5')，无按键返回0
 */
uint8_t HMI_Get_Key_Status(void)
{
    if (USART_RX_STA & 0x8000)
    {
        return USART_RX_BUF[0];
    }
    return 0;
}

/**
 * @brief 清除按键状态
 * @details 清除当前按键接收状态
 * @param None
 * @retval None
 */
void HMI_Clear_Key_Status(void)
{
    USART_RX_STA = 0;
}

// ==================== DDS控制函数实现 ====================

/**
 * @brief 开启DDS输出
 * @details 启用DDS输出并更新串口屏显示
 */
static void DDS_Enable_Output(void)
{
    dds_output_enabled = 1;

    // 配置DDS输出
    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);

    // 更新串口屏显示
    DDS_Update_Display();

}

/**
 * @brief 关闭DDS输出
 * @details 禁用DDS输出并更新串口屏显示
 */
static void DDS_Disable_Output(void)
{
    dds_output_enabled = 0;

    // 关闭DDS输出（设置幅度为0）
    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, 0);

    // 更新串口屏显示
    DDS_Update_Display();

}

/**
 * @brief 增加DDS频率100Hz
 * @details 将当前频率增加100Hz，如果输出已开启则立即应用
 */
static void DDS_Increase_Frequency(void)
{
    dds_frequency += 100; // 增加100Hz

    // 频率上限检查（最大50MHz）
    if (dds_frequency > 50000000) {
        dds_frequency = 50000000;
    }

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 减少DDS频率100Hz
 * @details 将当前频率减少100Hz，如果输出已开启则立即应用
 */
static void DDS_Decrease_Frequency(void)
{
    // 频率下限检查（最小100Hz）
    if (dds_frequency > 100) {
        dds_frequency -= 100; // 减少100Hz
    }

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 更新串口屏显示
 * @details 将当前DDS频率、波形类型和幅度分别显示到t4、t5、t6控件
 */
static void DDS_Update_Display(void)
{
    char freq_str[32];
    char amp_str[16];
    char status_str[32];

    // 1. 格式化频率字符串并显示到t4控件
    if (dds_frequency >= 1000000) {
        sprintf(freq_str, "%.2fMHz", dds_frequency / 1000000.0f);
    } else if (dds_frequency >= 1000) {
        sprintf(freq_str, "%.2fkHz", dds_frequency / 1000.0f);
    } else {
        sprintf(freq_str, "%luHz", dds_frequency);
    }
    HMI_Send_String(huart2, "t4", freq_str); // t4：频率

    // 2. 显示波形类型到t5控件
    const char* waveform_name = waveform_names[dds_waveform_type];
    HMI_Send_String(huart2, "t5", (char*)waveform_name); // t5：波形

    // 3. 格式化幅度字符串并显示到t6控件
    if (dds_output_enabled) {
        // 将幅度值转换为百分比显示（0-1023 -> 0-100%）
        uint8_t amp_percent = (dds_amplitude * 100) / 1023;
        sprintf(amp_str, "%d%%", amp_percent);
    } else {
        sprintf(amp_str, "OFF");
    }
    HMI_Send_String(huart2, "t6", amp_str); // t6：幅度
}

// ==================== 公共接口函数 ====================

/**
 * @brief 获取DDS输出状态
 * @details 返回当前DDS输出是否开启
 * @param None
 * @retval uint8_t 1=开启，0=关闭
 */
uint8_t HMI_Get_DDS_Status(void)
{
    return dds_output_enabled;
}

/**
 * @brief 获取DDS当前频率
 * @details 返回当前设置的DDS频率值
 * @param None
 * @retval uint32_t 频率值（Hz）
 */
uint32_t HMI_Get_DDS_Frequency(void)
{
    return dds_frequency;
}

/**
 * @brief 设置DDS频率
 * @details 直接设置DDS频率值
 * @param frequency 频率值（Hz）
 * @retval None
 */
void HMI_Set_DDS_Frequency(uint32_t frequency)
{
    // 频率范围检查
    if (frequency < 100) {
        frequency = 100; // 最小100Hz
    } else if (frequency > 50000000) {
        frequency = 50000000; // 最大50MHz
    }

    dds_frequency = frequency;

    // 如果DDS输出已开启，立即应用新频率
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 获取DDS波形类型
 * @details 返回当前设置的DDS波形类型
 * @param None
 * @retval uint8_t 波形类型（0=正弦波，1=方波，2=三角波）
 */
uint8_t HMI_Get_DDS_Waveform(void)
{
    return dds_waveform_type;
}

/**
 * @brief 设置DDS波形类型
 * @details 设置DDS输出的波形类型
 * @param waveform 波形类型（0=正弦波，1=方波，2=三角波）
 * @retval None
 */
void HMI_Set_DDS_Waveform(uint8_t waveform)
{
    // 波形类型范围检查
    if (waveform > 2) {
        waveform = 0; // 默认为正弦波
    }

    dds_waveform_type = waveform;

    // 注意：AD9959主要输出正弦波，这里的波形类型主要用于显示
    // 实际的波形控制可能需要额外的硬件配置

    // 如果DDS输出已开启，立即应用新设置
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 获取DDS幅度
 * @details 返回当前设置的DDS幅度值
 * @param None
 * @retval uint16_t 幅度值（0-1023）
 */
uint16_t HMI_Get_DDS_Amplitude(void)
{
    return dds_amplitude;
}

/**
 * @brief 设置DDS幅度
 * @details 设置DDS输出的幅度值
 * @param amplitude 幅度值（0-1023）
 * @retval None
 */
void HMI_Set_DDS_Amplitude(uint16_t amplitude)
{
    // 幅度范围检查
    if (amplitude > 1023) {
        amplitude = 1023; // 最大值限制
    }

    dds_amplitude = amplitude;

    // 如果DDS输出已开启，立即应用新幅度
    if (dds_output_enabled) {
        AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
    }

    // 更新串口屏显示
    DDS_Update_Display();
}

/**
 * @brief 初始化DDS控制显示
 * @details 初始化串口屏上的DDS状态显示
 * @param None
 * @retval None
 */
void HMI_DDS_Display_Init(void)
{
    // 初始化时更新一次显示，确保串口屏显示当前DDS状态
    DDS_Update_Display();

    // 发送初始化完成信息
    HMI_Send_String(huart2, "t0", "DDS Ready");

    my_printf(&huart1, "DDS控制显示初始化完成\r\n");
    my_printf(&huart1, "t4: 频率显示, t5: 波形显示, t6: 幅度显示\r\n");
}

/**
 * @brief DDS状态实时更新任务
 * @details 定期更新串口屏上的DDS状态信息，用于调度器调用
 * @param None
 * @retval None
 */
void HMI_DDS_Display_Update(void)
{
    // 调用内部显示更新函数
    DDS_Update_Display();
}

/**
 * @brief 处理参数设置命令
 * @details 解析A开头的参数设置命令，格式：A + 参数类型 + 数值 + ,
 * @param len 接收到的数据长度
 * @retval None
 */
static void Process_Parameter_Command(uint16_t len)
{
    len1 = len; // 保存数据长度
    temp_index = 0;
    temp_int = 0;

    // 提取参数部分：去掉开头的'A'
    strncpy(str, (char*)USART_RX_BUF + 1, len1 - 1);
    str[len1 - 1] = '\0'; // 确保字符串结束

    my_printf(&huart1, "接收到参数命令：%s\r\n", str);

    // 查找逗号位置
    for (i = 0; i < len1 - 1; i++) {
        if (str[i] == ',') {
            temp_index = i;
            break;
        }
    }

    if (temp_index > 0) {
        // 获取参数类型标识
        change_flag = str[0];
        my_printf(&huart1, "参数类型：%c\r\n", change_flag);

        // 提取数值部分
        strncpy(temp_str_to_int, str + 1, temp_index - 1);
        temp_str_to_int[temp_index - 1] = '\0';
        my_printf(&huart1, "数值字符串：%s\r\n", temp_str_to_int);

        // 转换为整数
        temp_int = atoi(temp_str_to_int);

        // 根据参数类型设置相应变量
        switch (change_flag) {
            case 'a': // 频率参数
                freq_my = (float)temp_int / 100.0f; // 除以100转换为MHz
                my_printf(&huart1, "设置频率：%.2f MHz\r\n", freq_my);

                // 应用到DDS频率（转换为Hz）
                dds_frequency = (uint32_t)(freq_my * 1000000);
                if (dds_output_enabled) {
                    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
                }
                DDS_Update_Display();
                break;

            case 'b': // 电压峰峰值参数
                vpp_my = temp_int;
                my_printf(&huart1, "设置电压峰峰值：%d mV\r\n", vpp_my);

                // 应用到DDS幅度（简单映射：1000mV对应最大幅度1023）
                dds_amplitude = (uint16_t)((vpp_my * 1023) / 1000);
                if (dds_amplitude > 1023) dds_amplitude = 1023;
                if (dds_output_enabled) {
                    AD9959_Single_Output(dds_channel, dds_frequency, dds_phase, dds_amplitude);
                }
                DDS_Update_Display();
                break;

            default:
                my_printf(&huart1, "未知参数类型：%c\r\n", change_flag);
                break;
        }
    } else {
        my_printf(&huart1, "参数格式错误：未找到逗号分隔符\r\n");
    }
}

