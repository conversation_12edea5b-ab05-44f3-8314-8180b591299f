/**
 * @file test_dds_control.c
 * @brief DDS控制功能测试程序
 * @details 测试串口屏DDS控制功能的各项功能
 * <AUTHOR>
 * @date 2025-07-30
 */

#include "bsp_system.h"
#include "hmi_key_handler.h"
#include "my_hmi.h"

/**
 * @brief DDS控制功能基本测试
 * @details 测试DDS控制的基本功能
 * @param None
 * @retval None
 */
void test_dds_basic_control(void)
{
    my_printf(&huart1, "=== DDS控制功能基本测试 ===\r\n");
    
    // 初始化DDS显示
    HMI_DDS_Display_Init();
    HAL_Delay(1000);
    
    // 测试频率设置
    my_printf(&huart1, "测试频率设置...\r\n");
    HMI_Set_DDS_Frequency(500000); // 设置为500kHz
    HAL_Delay(1000);
    
    HMI_Set_DDS_Frequency(2000000); // 设置为2MHz
    HAL_Delay(1000);
    
    // 测试波形类型设置
    my_printf(&huart1, "测试波形类型设置...\r\n");
    HMI_Set_DDS_Waveform(0); // 正弦波
    HAL_Delay(1000);
    
    HMI_Set_DDS_Waveform(1); // 方波
    HAL_Delay(1000);
    
    HMI_Set_DDS_Waveform(2); // 三角波
    HAL_Delay(1000);
    
    // 测试幅度设置
    my_printf(&huart1, "测试幅度设置...\r\n");
    HMI_Set_DDS_Amplitude(512); // 50%幅度
    HAL_Delay(1000);
    
    HMI_Set_DDS_Amplitude(1023); // 100%幅度
    HAL_Delay(1000);
    
    my_printf(&huart1, "DDS控制功能基本测试完成\r\n");
}

/**
 * @brief DDS显示功能测试
 * @details 测试串口屏显示功能
 * @param None
 * @retval None
 */
void test_dds_display(void)
{
    my_printf(&huart1, "=== DDS显示功能测试 ===\r\n");
    
    // 测试不同频率的显示格式
    uint32_t test_frequencies[] = {100, 1500, 15000, 150000, 1500000, 15000000};
    uint8_t freq_count = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    
    for (uint8_t i = 0; i < freq_count; i++) {
        my_printf(&huart1, "设置频率为: %lu Hz\r\n", test_frequencies[i]);
        HMI_Set_DDS_Frequency(test_frequencies[i]);
        HAL_Delay(2000); // 延时2秒观察显示
    }
    
    // 测试不同波形的显示
    const char* waveform_names[] = {"正弦波", "方波", "三角波"};
    for (uint8_t i = 0; i < 3; i++) {
        my_printf(&huart1, "设置波形为: %s\r\n", waveform_names[i]);
        HMI_Set_DDS_Waveform(i);
        HAL_Delay(2000);
    }
    
    // 测试不同幅度的显示
    uint16_t test_amplitudes[] = {0, 256, 512, 768, 1023};
    uint8_t amp_count = sizeof(test_amplitudes) / sizeof(test_amplitudes[0]);
    
    for (uint8_t i = 0; i < amp_count; i++) {
        uint8_t percent = (test_amplitudes[i] * 100) / 1023;
        my_printf(&huart1, "设置幅度为: %d%% (%d)\r\n", percent, test_amplitudes[i]);
        HMI_Set_DDS_Amplitude(test_amplitudes[i]);
        HAL_Delay(2000);
    }
    
    my_printf(&huart1, "DDS显示功能测试完成\r\n");
}

/**
 * @brief DDS按键模拟测试
 * @details 模拟串口屏按键操作
 * @param None
 * @retval None
 */
void test_dds_key_simulation(void)
{
    my_printf(&huart1, "=== DDS按键模拟测试 ===\r\n");
    
    // 模拟按键操作
    extern uint8_t USART_RX_BUF[200];
    extern uint16_t USART_RX_STA;
    
    // 模拟按键1：开启DDS
    my_printf(&huart1, "模拟按键1：开启DDS\r\n");
    USART_RX_BUF[0] = '1';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(2000);
    
    // 模拟按键3：增加频率
    my_printf(&huart1, "模拟按键3：增加频率100Hz\r\n");
    USART_RX_BUF[0] = '3';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(2000);
    
    // 再次增加频率
    my_printf(&huart1, "再次增加频率100Hz\r\n");
    USART_RX_BUF[0] = '3';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(2000);
    
    // 模拟按键4：减少频率
    my_printf(&huart1, "模拟按键4：减少频率100Hz\r\n");
    USART_RX_BUF[0] = '4';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(2000);
    
    // 模拟按键2：暂停DDS
    my_printf(&huart1, "模拟按键2：暂停DDS\r\n");
    USART_RX_BUF[0] = '2';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(2000);
    
    my_printf(&huart1, "DDS按键模拟测试完成\r\n");
}

/**
 * @brief DDS控制功能完整测试
 * @details 运行所有DDS控制测试
 * @param None
 * @retval None
 */
void test_dds_complete(void)
{
    my_printf(&huart1, "========================================\r\n");
    my_printf(&huart1, "    DDS控制功能完整测试开始\r\n");
    my_printf(&huart1, "========================================\r\n");
    
    // 运行各项测试
    test_dds_basic_control();
    HAL_Delay(2000);
    
    test_dds_display();
    HAL_Delay(2000);
    
    test_dds_key_simulation();
    
    my_printf(&huart1, "========================================\r\n");
    my_printf(&huart1, "    DDS控制功能完整测试完成\r\n");
    my_printf(&huart1, "========================================\r\n");
}

/**
 * @brief 获取DDS状态信息
 * @details 打印当前DDS的所有状态信息
 * @param None
 * @retval None
 */
void print_dds_status(void)
{
    my_printf(&huart1, "=== DDS当前状态 ===\r\n");
    my_printf(&huart1, "输出状态: %s\r\n", HMI_Get_DDS_Status() ? "开启" : "关闭");
    my_printf(&huart1, "频率: %lu Hz\r\n", HMI_Get_DDS_Frequency());
    
    const char* waveform_names[] = {"正弦波", "方波", "三角波"};
    uint8_t waveform = HMI_Get_DDS_Waveform();
    my_printf(&huart1, "波形: %s\r\n", waveform_names[waveform]);
    
    uint16_t amplitude = HMI_Get_DDS_Amplitude();
    uint8_t percent = (amplitude * 100) / 1023;
    my_printf(&huart1, "幅度: %d%% (%d/1023)\r\n", percent, amplitude);
    my_printf(&huart1, "==================\r\n");
}
