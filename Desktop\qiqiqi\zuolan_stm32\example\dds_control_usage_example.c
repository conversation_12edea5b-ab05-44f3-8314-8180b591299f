/**
 * @file dds_control_usage_example.c
 * @brief DDS控制功能使用示例
 * @details 展示如何在主程序中集成和使用DDS控制功能
 * <AUTHOR>
 * @date 2025-07-30
 */

#include "bsp_system.h"
#include "hmi_key_handler.h"
#include "my_hmi.h"

/**
 * @brief DDS控制功能集成示例
 * @details 展示如何在main函数中集成DDS控制功能
 * @param None
 * @retval None
 */
void dds_control_integration_example(void)
{
    my_printf(&huart1, "========================================\r\n");
    my_printf(&huart1, "    DDS控制功能集成示例\r\n");
    my_printf(&huart1, "========================================\r\n");
    
    // 1. 系统初始化
    my_printf(&huart1, "1. 系统初始化...\r\n");
    
    // 启动串口2中断接收（用于串口屏按键）
    HAL_UART_Receive_IT(&huart2, &rxTemp2, 1);
    
    // 初始化AD9959 DDS芯片
    AD9959_Init();
    
    // 初始化DDS控制显示
    HMI_DDS_Display_Init();
    
    my_printf(&huart1, "系统初始化完成\r\n");
    
    // 2. 显示功能说明
    my_printf(&huart1, "\r\n2. 功能说明：\r\n");
    my_printf(&huart1, "串口屏按键1: 开启DDS输出\r\n");
    my_printf(&huart1, "串口屏按键2: 暂停DDS输出\r\n");
    my_printf(&huart1, "串口屏按键3: 频率步进+100Hz\r\n");
    my_printf(&huart1, "串口屏按键4: 频率步进-100Hz\r\n");
    my_printf(&huart1, "串口屏按键5: AD波形显示\r\n");
    
    my_printf(&huart1, "\r\n3. 串口屏显示说明：\r\n");
    my_printf(&huart1, "t4控件: 显示DDS频率\r\n");
    my_printf(&huart1, "t5控件: 显示DDS波形类型\r\n");
    my_printf(&huart1, "t6控件: 显示DDS幅度\r\n");
    my_printf(&huart1, "t1控件: 显示DDS状态摘要\r\n");
    my_printf(&huart1, "t0控件: 显示操作提示\r\n");
    
    // 3. 演示程序化控制
    my_printf(&huart1, "\r\n4. 演示程序化控制：\r\n");
    
    // 设置初始参数
    HMI_Set_DDS_Frequency(1000000);  // 1MHz
    HMI_Set_DDS_Waveform(0);         // 正弦波
    HMI_Set_DDS_Amplitude(1023);     // 最大幅度
    my_printf(&huart1, "设置初始参数: 1MHz, 正弦波, 100%%幅度\r\n");
    HAL_Delay(2000);
    
    // 显示当前状态
    my_printf(&huart1, "当前DDS状态:\r\n");
    my_printf(&huart1, "- 频率: %lu Hz\r\n", HMI_Get_DDS_Frequency());
    my_printf(&huart1, "- 波形: %d (0=正弦波, 1=方波, 2=三角波)\r\n", HMI_Get_DDS_Waveform());
    my_printf(&huart1, "- 幅度: %d/1023\r\n", HMI_Get_DDS_Amplitude());
    my_printf(&huart1, "- 状态: %s\r\n", HMI_Get_DDS_Status() ? "开启" : "关闭");
    
    my_printf(&huart1, "\r\n5. 系统准备就绪，等待串口屏按键操作...\r\n");
    my_printf(&huart1, "========================================\r\n");
}

/**
 * @brief DDS控制主循环示例
 * @details 展示如何在主循环中处理DDS控制
 * @param None
 * @retval None
 */
void dds_control_main_loop_example(void)
{
    static uint32_t last_status_update = 0;
    static uint32_t last_key_check = 0;
    uint32_t current_tick = HAL_GetTick();
    
    // 每10ms检查一次串口屏按键
    if (current_tick - last_key_check >= 10) {
        HMI_Key_Process(); // 处理串口屏按键
        last_key_check = current_tick;
    }
    
    // 每5秒打印一次状态信息（可选）
    if (current_tick - last_status_update >= 5000) {
        my_printf(&huart1, "DDS状态: %s, 频率: %lu Hz, 幅度: %d%%\r\n",
                  HMI_Get_DDS_Status() ? "ON" : "OFF",
                  HMI_Get_DDS_Frequency(),
                  (HMI_Get_DDS_Amplitude() * 100) / 1023);
        last_status_update = current_tick;
    }
}

/**
 * @brief 完整的main函数示例
 * @details 展示完整的main函数集成方式
 * @param None
 * @retval None
 */
void complete_main_function_example(void)
{
    my_printf(&huart1, "这是一个完整的main函数集成示例：\r\n\r\n");
    
    my_printf(&huart1, "int main(void)\r\n");
    my_printf(&huart1, "{\r\n");
    my_printf(&huart1, "    // 1. HAL库初始化\r\n");
    my_printf(&huart1, "    HAL_Init();\r\n");
    my_printf(&huart1, "    SystemClock_Config();\r\n\r\n");
    
    my_printf(&huart1, "    // 2. 外设初始化\r\n");
    my_printf(&huart1, "    MX_GPIO_Init();\r\n");
    my_printf(&huart1, "    MX_USART1_UART_Init();\r\n");
    my_printf(&huart1, "    MX_USART2_UART_Init();\r\n");
    my_printf(&huart1, "    MX_DAC_Init();\r\n\r\n");
    
    my_printf(&huart1, "    // 3. 启动串口中断\r\n");
    my_printf(&huart1, "    HAL_UART_Receive_IT(&huart1, &rxTemp1, 1);\r\n");
    my_printf(&huart1, "    HAL_UART_Receive_IT(&huart2, &rxTemp2, 1);\r\n\r\n");
    
    my_printf(&huart1, "    // 4. 初始化各模块\r\n");
    my_printf(&huart1, "    AD9959_Init();              // 初始化DDS芯片\r\n");
    my_printf(&huart1, "    HMI_DDS_Display_Init();     // 初始化DDS控制显示\r\n");
    my_printf(&huart1, "    scheduler_init();           // 初始化调度器\r\n\r\n");
    
    my_printf(&huart1, "    // 5. 主循环\r\n");
    my_printf(&huart1, "    while(1) {\r\n");
    my_printf(&huart1, "        scheduler_run();        // 运行调度器（包含HMI_Key_Process）\r\n");
    my_printf(&huart1, "        // 其他任务...\r\n");
    my_printf(&huart1, "    }\r\n");
    my_printf(&huart1, "}\r\n\r\n");
    
    my_printf(&huart1, "注意：调度器已经包含了HMI_Key_Process任务，\r\n");
    my_printf(&huart1, "      所以不需要在主循环中单独调用。\r\n");
}

/**
 * @brief DDS控制API使用示例
 * @details 展示如何使用DDS控制API
 * @param None
 * @retval None
 */
void dds_control_api_example(void)
{
    my_printf(&huart1, "=== DDS控制API使用示例 ===\r\n");
    
    // 1. 设置DDS参数
    my_printf(&huart1, "1. 设置DDS参数：\r\n");
    HMI_Set_DDS_Frequency(2000000);  // 设置频率为2MHz
    HMI_Set_DDS_Waveform(1);         // 设置为方波
    HMI_Set_DDS_Amplitude(512);      // 设置为50%幅度
    my_printf(&huart1, "   频率: 2MHz, 波形: 方波, 幅度: 50%%\r\n");
    HAL_Delay(2000);
    
    // 2. 读取DDS参数
    my_printf(&huart1, "2. 读取DDS参数：\r\n");
    uint32_t freq = HMI_Get_DDS_Frequency();
    uint8_t waveform = HMI_Get_DDS_Waveform();
    uint16_t amplitude = HMI_Get_DDS_Amplitude();
    uint8_t status = HMI_Get_DDS_Status();
    
    my_printf(&huart1, "   频率: %lu Hz\r\n", freq);
    my_printf(&huart1, "   波形: %d\r\n", waveform);
    my_printf(&huart1, "   幅度: %d/1023 (%d%%)\r\n", amplitude, (amplitude * 100) / 1023);
    my_printf(&huart1, "   状态: %s\r\n", status ? "开启" : "关闭");
    
    // 3. 演示频率扫描
    my_printf(&huart1, "3. 演示频率扫描：\r\n");
    for (uint32_t f = 1000000; f <= 5000000; f += 500000) {
        HMI_Set_DDS_Frequency(f);
        my_printf(&huart1, "   设置频率: %lu Hz\r\n", f);
        HAL_Delay(1000);
    }
    
    my_printf(&huart1, "API使用示例完成\r\n");
}

/**
 * @brief 运行所有DDS控制示例
 * @details 运行所有示例程序
 * @param None
 * @retval None
 */
void run_all_dds_examples(void)
{
    my_printf(&huart1, "开始运行所有DDS控制示例...\r\n\r\n");
    
    // 运行各个示例
    dds_control_integration_example();
    HAL_Delay(3000);
    
    complete_main_function_example();
    HAL_Delay(2000);
    
    dds_control_api_example();
    HAL_Delay(2000);
    
    my_printf(&huart1, "所有DDS控制示例运行完成！\r\n");
    my_printf(&huart1, "现在可以使用串口屏按键进行实际操作。\r\n");
}
