/**
 * @file test_dds_control.h
 * @brief DDS控制功能测试程序头文件
 * @details 声明DDS控制功能测试相关的函数
 * <AUTHOR>
 * @date 2025-07-30
 */

#ifndef __TEST_DDS_CONTROL_H__
#define __TEST_DDS_CONTROL_H__

#include "stm32f4xx_hal.h"
#include "stdint.h"

// 测试函数声明

/**
 * @brief DDS控制功能基本测试
 * @details 测试DDS控制的基本功能
 * @param None
 * @retval None
 */
void test_dds_basic_control(void);

/**
 * @brief DDS显示功能测试
 * @details 测试串口屏显示功能
 * @param None
 * @retval None
 */
void test_dds_display(void);

/**
 * @brief DDS按键模拟测试
 * @details 模拟串口屏按键操作
 * @param None
 * @retval None
 */
void test_dds_key_simulation(void);

/**
 * @brief DDS控制功能完整测试
 * @details 运行所有DDS控制测试
 * @param None
 * @retval None
 */
void test_dds_complete(void);

/**
 * @brief 获取DDS状态信息
 * @details 打印当前DDS的所有状态信息
 * @param None
 * @retval None
 */
void print_dds_status(void);

#endif // __TEST_DDS_CONTROL_H__
