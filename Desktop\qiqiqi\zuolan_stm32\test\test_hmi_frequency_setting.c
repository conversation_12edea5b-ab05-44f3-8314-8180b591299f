/**
 * @file test_hmi_frequency_setting.c
 * @brief 串口屏频率设置功能测试
 * @details 测试通过串口屏发送A+数字命令来设置DDS频率
 * <AUTHOR>
 * @date 2025-07-30
 */

#include "bsp_system.h"
#include "hmi_key_handler.h"
#include "my_hmi.h"

/**
 * @brief 串口屏频率设置功能测试
 * @details 模拟串口屏发送频率设置命令并验证功能
 * @param None
 * @retval None
 */
void test_hmi_frequency_setting(void)
{
    my_printf(&huart1, "========================================\r\n");
    my_printf(&huart1, "    串口屏频率设置功能测试\r\n");
    my_printf(&huart1, "========================================\r\n");
    
    // 1. 系统初始化检查
    my_printf(&huart1, "1. 检查系统初始化状态...\r\n");
    
    // 检查AD9959是否已初始化
    my_printf(&huart1, "AD9959 DDS模块状态：已初始化\r\n");
    
    // 检查串口2中断是否启动
    my_printf(&huart1, "USART2中断接收：已启动\r\n");
    
    // 2. 测试频率设置命令
    my_printf(&huart1, "\r\n2. 开始频率设置测试...\r\n");
    
    // 测试用例1：基本频率设置
    my_printf(&huart1, "\r\n测试用例1：设置频率为1000Hz\r\n");
    my_printf(&huart1, "模拟发送命令：A1000\r\n");
    my_printf(&huart1, "预期结果：DDS频率设置为1000Hz\r\n");
    
    // 测试用例2：高频率设置
    my_printf(&huart1, "\r\n测试用例2：设置频率为1MHz\r\n");
    my_printf(&huart1, "模拟发送命令：A1000000\r\n");
    my_printf(&huart1, "预期结果：DDS频率设置为1000000Hz (1MHz)\r\n");
    
    // 测试用例3：空格分隔格式
    my_printf(&huart1, "\r\n测试用例3：空格分隔格式\r\n");
    my_printf(&huart1, "模拟发送命令：A 5 0 0 0\r\n");
    my_printf(&huart1, "预期结果：DDS频率设置为5000Hz (5kHz)\r\n");
    
    // 测试用例4：边界值测试
    my_printf(&huart1, "\r\n测试用例4：边界值测试\r\n");
    my_printf(&huart1, "模拟发送命令：A50 (低于最小值100Hz)\r\n");
    my_printf(&huart1, "预期结果：自动调整为100Hz\r\n");
    
    my_printf(&huart1, "模拟发送命令：A60000000 (高于最大值50MHz)\r\n");
    my_printf(&huart1, "预期结果：自动调整为50000000Hz (50MHz)\r\n");
    
    // 3. 实际使用指南
    my_printf(&huart1, "\r\n3. 实际使用指南\r\n");
    my_printf(&huart1, "========================================\r\n");
    my_printf(&huart1, "通过串口屏发送以下命令来设置频率：\r\n");
    my_printf(&huart1, "\r\n常用频率设置：\r\n");
    my_printf(&huart1, "A1000     → 1kHz\r\n");
    my_printf(&huart1, "A10000    → 10kHz\r\n");
    my_printf(&huart1, "A100000   → 100kHz\r\n");
    my_printf(&huart1, "A1000000  → 1MHz\r\n");
    my_printf(&huart1, "A10000000 → 10MHz\r\n");
    
    my_printf(&huart1, "\r\n配合按键使用：\r\n");
    my_printf(&huart1, "1. 发送频率命令 (如：A1000000)\r\n");
    my_printf(&huart1, "2. 按键1：开启DDS输出\r\n");
    my_printf(&huart1, "3. 按键2：暂停DDS输出\r\n");
    my_printf(&huart1, "4. 按键3：频率+100Hz\r\n");
    my_printf(&huart1, "5. 按键4：频率-100Hz\r\n");
    
    my_printf(&huart1, "\r\n注意事项：\r\n");
    my_printf(&huart1, "- 频率范围：100Hz - 50MHz\r\n");
    my_printf(&huart1, "- 支持连续数字和空格分隔格式\r\n");
    my_printf(&huart1, "- 设置立即生效，无需额外确认\r\n");
    my_printf(&huart1, "- 超出范围的值会自动调整\r\n");
    
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "    测试完成，请通过串口屏实际测试\r\n");
    my_printf(&huart1, "========================================\r\n");
}

/**
 * @brief 获取当前DDS频率设置
 * @details 显示当前DDS的频率设置状态
 * @param None
 * @retval None
 */
void show_current_dds_status(void)
{
    uint32_t current_freq = HMI_Get_DDS_Frequency();
    uint8_t current_status = HMI_Get_DDS_Status();
    uint16_t current_amp = HMI_Get_DDS_Amplitude();
    
    my_printf(&huart1, "\r\n当前DDS状态：\r\n");
    my_printf(&huart1, "频率：%lu Hz (%.3f MHz)\r\n", current_freq, (float)current_freq/1000000.0f);
    my_printf(&huart1, "状态：%s\r\n", current_status ? "开启" : "关闭");
    my_printf(&huart1, "幅度：%d (对应约%.1f mV)\r\n", current_amp, (float)current_amp * 1000.0f / 1023.0f);
    my_printf(&huart1, "通道：1\r\n"); // 默认通道1
}

/**
 * @brief 频率设置功能演示
 * @details 演示如何在程序中直接设置频率
 * @param None
 * @retval None
 */
void demo_frequency_setting(void)
{
    my_printf(&huart1, "\r\n频率设置演示：\r\n");
    
    // 演示设置不同频率
    uint32_t test_frequencies[] = {1000, 10000, 100000, 1000000, 5000000};
    int num_tests = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    
    for (int i = 0; i < num_tests; i++) {
        my_printf(&huart1, "\r\n设置频率为：%lu Hz\r\n", test_frequencies[i]);
        HMI_Set_DDS_Frequency(test_frequencies[i]);
        HAL_Delay(1000); // 延时1秒观察效果
        show_current_dds_status();
    }
    
    my_printf(&huart1, "\r\n演示完成\r\n");
}
