# 串口屏DDS参数设置功能说明

## 功能概述

本功能实现了通过串口屏发送参数命令来直接控制DDS模块的频率、幅度和通道设置。支持实时参数调整，命令格式简洁易用。

## 命令格式

### 基本格式
```
A + 参数类型 + 数值 + ,
```

### 支持的参数类型

#### 1. 频率设置 (a)
- **格式**: `Aa数值,`
- **单位**: 数值/100 = MHz
- **示例**: 
  - `Aa100,` → 设置频率为1.00MHz
  - `Aa250,` → 设置频率为2.50MHz
  - `Aa1000,` → 设置频率为10.00MHz
- **范围**: 100Hz - 50MHz

#### 2. 幅度设置 (b)
- **格式**: `Ab数值,`
- **单位**: mV (毫伏)
- **示例**:
  - `Ab500,` → 设置幅度为500mV
  - `Ab1000,` → 设置幅度为1000mV (最大值)
  - `Ab100,` → 设置幅度为100mV
- **范围**: 0-1000mV

#### 3. 通道选择 (c)
- **格式**: `Ac数值,`
- **单位**: 通道号
- **示例**:
  - `Ac0,` → 选择通道0
  - `Ac1,` → 选择通道1
  - `Ac2,` → 选择通道2
  - `Ac3,` → 选择通道3
- **范围**: 0-3

## 使用方法

### 1. 硬件连接
- 确保串口屏连接到USART2
- 确保AD9959 DDS模块正确连接

### 2. 软件配置
确保在main函数中已完成以下初始化：
```c
HAL_UART_Receive_IT(&huart2, &rxTemp2, 1); // 启动串口2中断接收
AD9959_Init();                              // 初始化AD9959芯片
scheduler_init();                           // 初始化调度器
```

### 3. 发送命令
通过串口屏发送命令，系统会自动解析并应用到DDS硬件。

## 命令示例

### 完整设置流程
```
1. Ac1,     → 选择通道1
2. Aa200,   → 设置频率为2.00MHz
3. Ab800,   → 设置幅度为800mV
4. 1        → 开启DDS输出（使用按键命令）
```

### 快速调频示例
```
Aa100,   → 1.00MHz
Aa150,   → 1.50MHz
Aa200,   → 2.00MHz
Aa500,   → 5.00MHz
```

### 幅度调节示例
```
Ab100,   → 100mV (低幅度)
Ab500,   → 500mV (中等幅度)
Ab1000,  → 1000mV (最大幅度)
```

## 反馈信息

系统会通过USART1返回详细的执行信息：

### 成功设置示例
```
接收到参数命令：a200,
参数类型：a
数值字符串：200
设置频率：2.00 MHz
DDS频率已设置为：2000000 Hz
```

### 错误处理示例
```
频率过高，已调整为50MHz
通道号错误，有效范围：0-3
参数格式错误：未找到逗号分隔符
```

## 技术特性

### 1. 实时控制
- 参数设置立即生效
- 无需重启或额外确认

### 2. 范围保护
- 自动检查参数范围
- 超出范围时自动调整到有效值

### 3. 状态同步
- 参数变更后自动更新串口屏显示
- 保持界面与实际状态一致

### 4. 错误处理
- 详细的错误提示信息
- 格式错误时提供正确格式说明

## 注意事项

1. **命令格式**: 必须严格按照格式发送，包括逗号结尾
2. **数值范围**: 超出范围的数值会被自动调整
3. **通道切换**: 切换通道后当前频率和幅度设置会应用到新通道
4. **实时生效**: 所有参数设置立即应用到硬件，无需额外操作

## 扩展功能

### 计数器命令
- **格式**: `1`
- **功能**: 发送递增计数器到串口屏文本框t0
- **用途**: 测试串口屏通信和显示功能

## 故障排除

### 1. 命令无响应
- 检查串口屏连接
- 确认USART2中断是否正常启动

### 2. 参数设置无效
- 检查AD9959硬件连接
- 确认DDS模块初始化是否完成

### 3. 显示不更新
- 检查串口屏通信协议
- 确认HMI显示更新任务是否在调度器中运行

## 开发者信息

- **模块**: hmi_key_handler.c/h
- **主要函数**: `Process_Parameter_Command()`
- **调度任务**: `HMI_Key_Process()` (10ms周期)
- **显示更新**: `HMI_DDS_Display_Update()` (200ms周期)
